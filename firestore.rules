rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isValidInterviewData() {
      return request.resource.data.keys().hasAll(['userId', 'role', 'type', 'questions', 'createdAt']) &&
             request.resource.data.userId == request.auth.uid &&
             request.resource.data.role is string &&
             request.resource.data.type in ['technical', 'behavioral', 'mixed'] &&
             request.resource.data.questions is list &&
             request.resource.data.questions.size() > 0 &&
             request.resource.data.questions.size() <= 20;
    }
    
    function isValidFeedbackData() {
      return request.resource.data.keys().hasAll(['interviewId', 'userId', 'totalScore', 'createdAt']) &&
             request.resource.data.userId == request.auth.uid &&
             request.resource.data.totalScore is number &&
             request.resource.data.totalScore >= 0 &&
             request.resource.data.totalScore <= 10;
    }
    
    function isValidUserUpdate() {
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(['credits', 'lastActive', 'profileImage', 'lastCreditRenewalAt', 'resumeCredits', 'lastResumeCreditRenewalAt']);
    }

    // Users collection
    match /users/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
      allow update: if isAuthenticated() && isOwner(userId) && isValidUserUpdate();
    }

    // Interviews collection
    match /interviews/{interviewId} {
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isValidInterviewData();
      allow update: if isAuthenticated() && isOwner(resource.data.userId) &&
                   // Only allow updating specific fields
                   request.resource.data.diff(resource.data).affectedKeys()
                     .hasOnly(['status', 'responses', 'updatedAt', 'completedAt', 'feedbackId', 'finalScore']);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Feedback collection
    match /feedback/{feedbackId} {
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isValidFeedbackData();
      allow update: if false; // Feedback should be immutable once created
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Interview templates (public read, admin write)
    match /interview_templates/{templateId} {
      allow read: if true; // Public read access for templates
      allow write: if false; // Only admin can write (handled server-side)
    }

    // User analytics (aggregated data)
    match /analytics/{userId} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow write: if false; // Only server-side writes
    }

    // System collections (admin only)
    match /system/{document=**} {
      allow read, write: if false; // Only server-side access
    }

    // Default deny all other documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
