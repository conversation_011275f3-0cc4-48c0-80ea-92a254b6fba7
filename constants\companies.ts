// Company logos mapping
export const COMPANY_LOGOS: Record<string, string> = {
  "Google": "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/google/google-original.svg",
  "Apple": "https://logo.clearbit.com/apple.com",
  "Microsoft": "https://logo.clearbit.com/microsoft.com",
  "Amazon": "https://logo.clearbit.com/amazon.com",
  "Meta": "https://logo.clearbit.com/meta.com",
  "Netflix": "https://logo.clearbit.com/netflix.com",
  "Tesla": "https://logo.clearbit.com/tesla.com",
  "Spotify": "https://storage.googleapis.com/pr-newsroom-wp/1/2018/11/Spotify_Logo_CMYK_Green.png",
  "Airbnb": "https://logo.clearbit.com/airbnb.com",
  "Uber": "https://logo.clearbit.com/uber.com",
  "LinkedIn": "https://logo.clearbit.com/linkedin.com",
  "Salesforce": "https://logo.clearbit.com/salesforce.com",
  "Adobe": "https://logo.clearbit.com/adobe.com",
  "Oracle": "https://logo.clearbit.com/oracle.com",
  "IBM": "https://logo.clearbit.com/ibm.com",
  "Intel": "https://logo.clearbit.com/intel.com",
  "NVIDIA": "https://logo.clearbit.com/nvidia.com",
  "AMD": "https://logo.clearbit.com/amd.com",
  "Qualcomm": "https://logo.clearbit.com/qualcomm.com",
  "Cisco": "https://logo.clearbit.com/cisco.com",
  "VMware": "https://logo.clearbit.com/vmware.com",
  "ServiceNow": "https://logo.clearbit.com/servicenow.com",
  "Snowflake": "https://logo.clearbit.com/snowflake.com",
  "Palantir": "https://logo.clearbit.com/palantir.com",
  "Databricks": "https://logo.clearbit.com/databricks.com",
  "Stripe": "https://logo.clearbit.com/stripe.com",
  "Square": "https://logo.clearbit.com/squareup.com",
  "PayPal": "https://logo.clearbit.com/paypal.com",
  "Zoom": "https://logo.clearbit.com/zoom.us",
  "Slack": "https://logo.clearbit.com/slack.com",
  "Atlassian": "https://logo.clearbit.com/atlassian.com",
  "Shopify": "https://logo.clearbit.com/shopify.com",
  "Twilio": "https://logo.clearbit.com/twilio.com",
  "Okta": "https://logo.clearbit.com/okta.com",
  "Cloudflare": "https://logo.clearbit.com/cloudflare.com",
  "MongoDB": "https://logo.clearbit.com/mongodb.com",
  "Elastic": "https://logo.clearbit.com/elastic.co",
  "JPMorgan Chase": "https://logo.clearbit.com/jpmorganchase.com",
  "Goldman Sachs": "https://logo.clearbit.com/goldmansachs.com",
  "Morgan Stanley": "https://logo.clearbit.com/morganstanley.com",
  "Bank of America": "https://logo.clearbit.com/bankofamerica.com",
  "Wells Fargo": "https://logo.clearbit.com/wellsfargo.com",
  "Citigroup": "https://logo.clearbit.com/citigroup.com",
  "American Express": "https://logo.clearbit.com/americanexpress.com",
  "Visa": "https://logo.clearbit.com/visa.com",
  "Mastercard": "https://logo.clearbit.com/mastercard.com",
  "BlackRock": "https://logo.clearbit.com/blackrock.com",
  "Fidelity": "https://logo.clearbit.com/fidelity.com",
  "Charles Schwab": "https://logo.clearbit.com/schwab.com",
  "Capital One": "https://logo.clearbit.com/capitalone.com",
  "Robinhood": "https://logo.clearbit.com/robinhood.com",
  "McKinsey & Company": "https://logo.clearbit.com/mckinsey.com",
  "Boston Consulting Group": "https://logo.clearbit.com/bcg.com",
  "Bain & Company": "https://logo.clearbit.com/bain.com",
  "Deloitte": "https://logo.clearbit.com/deloitte.com",
  "PwC": "https://logo.clearbit.com/pwc.com",
  "EY": "https://logo.clearbit.com/ey.com",
  "KPMG": "https://logo.clearbit.com/kpmg.com",
  "Accenture": "https://logo.clearbit.com/accenture.com",
  "Walmart": "https://logo.clearbit.com/walmart.com",
  "Target": "https://logo.clearbit.com/target.com",
  "Costco": "https://logo.clearbit.com/costco.com",
  "Home Depot": "https://logo.clearbit.com/homedepot.com",
  "Best Buy": "https://logo.clearbit.com/bestbuy.com",
  "Wayfair": "https://logo.clearbit.com/wayfair.com",
  "eBay": "https://logo.clearbit.com/ebay.com",
  "Etsy": "https://logo.clearbit.com/etsy.com",
  "Chewy": "https://logo.clearbit.com/chewy.com",
  "Instacart": "https://logo.clearbit.com/instacart.com",
  "DoorDash": "https://logo.clearbit.com/doordash.com",
  "Grubhub": "https://logo.clearbit.com/grubhub.com",
  "Johnson & Johnson": "https://logo.clearbit.com/jnj.com",
  "Pfizer": "https://logo.clearbit.com/pfizer.com",
  "Moderna": "https://logo.clearbit.com/modernatx.com",
  "Ford": "https://logo.clearbit.com/ford.com",
  "General Motors": "https://logo.clearbit.com/gm.com",
  "Toyota": "https://logo.clearbit.com/toyota.com",
  "Honda": "https://logo.clearbit.com/honda.com",
  "BMW": "https://logo.clearbit.com/bmw.com",
  "Mercedes-Benz": "https://logo.clearbit.com/mercedes-benz.com",
  "Boeing": "https://logo.clearbit.com/boeing.com",
  "SpaceX": "https://logo.clearbit.com/spacex.com",
  "Disney": "https://logo.clearbit.com/disney.com",
  "Comcast": "https://logo.clearbit.com/comcast.com",
  "Sony": "https://logo.clearbit.com/sony.com",
  "OpenAI": "https://logo.clearbit.com/openai.com",
  "Figma": "https://logo.clearbit.com/figma.com",
  "Notion": "https://logo.clearbit.com/notion.so",
  "Canva": "https://logo.clearbit.com/canva.com",
  "Discord": "https://logo.clearbit.com/discord.com",
  "Miro": "https://logo.clearbit.com/miro.com"
};

// Function to get company logo
export function getCompanyLogo(companyName: string): string {
  return COMPANY_LOGOS[companyName] || `https://logo.clearbit.com/${companyName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '')}.com`;
}

// Top 100 companies database for autocomplete and search
export const TOP_COMPANIES = [
  // FAANG + Major Tech
  "Google", "Apple", "Microsoft", "Amazon", "Meta", "Netflix", "Tesla", "Spotify",
  "Airbnb", "Uber", "LinkedIn", "Salesforce", "Adobe", "Oracle", "IBM", "Intel",
  "NVIDIA", "AMD", "Qualcomm", "Cisco", "VMware", "ServiceNow", "Snowflake",
  "Palantir", "Databricks", "Stripe", "Square", "PayPal", "Zoom", "Slack",
  "Atlassian", "Shopify", "Twilio", "Okta", "Cloudflare", "MongoDB", "Elastic",
  
  // Financial Services
  "JPMorgan Chase", "Goldman Sachs", "Morgan Stanley", "Bank of America", 
  "Wells Fargo", "Citigroup", "American Express", "Visa", "Mastercard",
  "BlackRock", "Fidelity", "Charles Schwab", "Capital One", "Robinhood",
  
  // Consulting & Professional Services
  "McKinsey & Company", "Boston Consulting Group", "Bain & Company", 
  "Deloitte", "PwC", "EY", "KPMG", "Accenture", "IBM Consulting",
  
  // E-commerce & Retail
  "Walmart", "Target", "Costco", "Home Depot", "Lowe's", "Best Buy",
  "Wayfair", "eBay", "Etsy", "Chewy", "Instacart", "DoorDash", "Grubhub",
  
  // Healthcare & Biotech
  "Johnson & Johnson", "Pfizer", "Moderna", "AbbVie", "Merck", "Bristol Myers Squibb",
  "Eli Lilly", "Amgen", "Gilead Sciences", "Biogen", "Regeneron", "Illumina",
  "Teladoc", "Veracyte", "10x Genomics",
  
  // Automotive
  "Ford", "General Motors", "Stellantis", "Toyota", "Honda", "Nissan",
  "BMW", "Mercedes-Benz", "Volkswagen", "Rivian", "Lucid Motors",
  
  // Aerospace & Defense
  "Boeing", "Lockheed Martin", "Raytheon", "Northrop Grumman", "General Dynamics",
  "SpaceX", "Blue Origin",
  
  // Energy & Utilities
  "ExxonMobil", "Chevron", "ConocoPhillips", "NextEra Energy", "Enphase Energy",
  "SolarEdge", "First Solar",
  
  // Media & Entertainment
  "Disney", "Comcast", "Warner Bros Discovery", "Paramount", "Sony", "Universal",
  "Roku", "Peloton", "Unity", "Roblox", "Electronic Arts", "Activision Blizzard",
  
  // Startups & Unicorns
  "OpenAI", "Anthropic", "Canva", "Figma", "Notion", "Airtable", "Zapier",
  "Calendly", "Loom", "Miro", "Asana", "Monday.com", "Slack", "Discord"
];

// Create normalized search map for case-insensitive matching
export const COMPANY_SEARCH_MAP = new Map(
  TOP_COMPANIES.map(company => [
    company.toLowerCase().replace(/[^a-z0-9]/g, ''), 
    company
  ])
);

// Function to find company by fuzzy matching
export function findCompanyMatch(query: string): string | null {
  const normalizedQuery = query.toLowerCase().replace(/[^a-z0-9]/g, '');
  
  // Exact match first
  if (COMPANY_SEARCH_MAP.has(normalizedQuery)) {
    return COMPANY_SEARCH_MAP.get(normalizedQuery)!;
  }
  
  // Partial match
  for (const [normalized, original] of COMPANY_SEARCH_MAP.entries()) {
    if (normalized.includes(normalizedQuery) || normalizedQuery.includes(normalized)) {
      return original;
    }
  }
  
  return null;
}

// Function to get autocomplete suggestions
export function getAutocompleteSuggestions(query: string, limit: number = 5): string[] {
  if (!query || query.length < 1) return [];
  
  const normalizedQuery = query.toLowerCase();
  const suggestions: string[] = [];
  
  for (const company of TOP_COMPANIES) {
    if (company.toLowerCase().startsWith(normalizedQuery)) {
      suggestions.push(company);
    }
    if (suggestions.length >= limit) break;
  }
  
  // If no prefix matches, try contains matching
  if (suggestions.length < limit) {
    for (const company of TOP_COMPANIES) {
      if (!suggestions.includes(company) && 
          company.toLowerCase().includes(normalizedQuery)) {
        suggestions.push(company);
        if (suggestions.length >= limit) break;
      }
    }
  }
  
  return suggestions;
}
